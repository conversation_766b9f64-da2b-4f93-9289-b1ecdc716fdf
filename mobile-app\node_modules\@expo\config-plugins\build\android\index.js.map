{"version": 3, "file": "index.js", "names": ["AllowBackup", "data", "_interopRequireWildcard", "require", "Object", "defineProperty", "exports", "enumerable", "get", "BuildProperties", "Colors", "EasBuild", "GoogleMapsApiKey", "GoogleServices", "IntentFilters", "Locales", "Manifest", "Name", "Orientation", "Package", "Paths", "Permissions", "PrimaryColor", "Properties", "Resources", "Scheme", "StatusBar", "Strings", "Styles", "Updates", "Version", "WindowSoftInputMode", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set"], "sources": ["../../src/android/index.ts"], "sourcesContent": ["import * as AllowBackup from './AllowBackup';\nimport * as BuildProperties from './BuildProperties';\nimport * as Colors from './Colors';\nimport * as EasBuild from './EasBuild';\nimport * as GoogleMapsApiKey from './GoogleMapsApiKey';\nimport * as GoogleServices from './GoogleServices';\nimport * as IntentFilters from './IntentFilters';\nimport * as Locales from './Locales';\nimport * as Manifest from './Manifest';\nimport * as Name from './Name';\nimport * as Orientation from './Orientation';\nimport * as Package from './Package';\nimport * as Paths from './Paths';\nimport * as Permissions from './Permissions';\nimport * as PrimaryColor from './PrimaryColor';\nimport * as Properties from './Properties';\nimport * as Resources from './Resources';\nimport * as Scheme from './Scheme';\nimport * as StatusBar from './StatusBar';\nimport * as Strings from './Strings';\nimport * as Styles from './Styles';\nimport * as Updates from './Updates';\nimport * as Version from './Version';\nimport * as WindowSoftInputMode from './WindowSoftInputMode';\n\nexport { Manifest, Colors, Paths, Permissions, Properties, Resources, Scheme, Strings, Styles };\n\nexport {\n  AllowBackup,\n  BuildProperties,\n  EasBuild,\n  GoogleMapsApiKey,\n  GoogleServices,\n  IntentFilters,\n  Name,\n  Locales,\n  Orientation,\n  Package,\n  PrimaryColor,\n  StatusBar,\n  Updates,\n  Version,\n  WindowSoftInputMode,\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAH,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAR,WAAA;EAAA;AAAA;AAC7C,SAAAS,gBAAA;EAAA,MAAAR,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAM,eAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAC,eAAA;EAAA;AAAA;AACrD,SAAAC,OAAA;EAAA,MAAAT,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAO,MAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAE,MAAA;EAAA;AAAA;AACnC,SAAAC,SAAA;EAAA,MAAAV,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAQ,QAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAG,QAAA;EAAA;AAAA;AACvC,SAAAC,iBAAA;EAAA,MAAAX,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAS,gBAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAI,gBAAA;EAAA;AAAA;AACvD,SAAAC,eAAA;EAAA,MAAAZ,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAU,cAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAK,cAAA;EAAA;AAAA;AACnD,SAAAC,cAAA;EAAA,MAAAb,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAW,aAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAM,aAAA;EAAA;AAAA;AACjD,SAAAC,QAAA;EAAA,MAAAd,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAY,OAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAO,OAAA;EAAA;AAAA;AACrC,SAAAC,SAAA;EAAA,MAAAf,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAa,QAAA,YAAAA,CAAA;IAAA,OAAAf,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAQ,QAAA;EAAA;AAAA;AACvC,SAAAC,KAAA;EAAA,MAAAhB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAc,IAAA,YAAAA,CAAA;IAAA,OAAAhB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+BG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAS,IAAA;EAAA;AAAA;AAC/B,SAAAC,YAAA;EAAA,MAAAjB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAe,WAAA,YAAAA,CAAA;IAAA,OAAAjB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAU,WAAA;EAAA;AAAA;AAC7C,SAAAC,QAAA;EAAA,MAAAlB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAgB,OAAA,YAAAA,CAAA;IAAA,OAAAlB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAW,OAAA;EAAA;AAAA;AACrC,SAAAC,MAAA;EAAA,MAAAnB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAiB,KAAA,YAAAA,CAAA;IAAA,OAAAnB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAY,KAAA;EAAA;AAAA;AACjC,SAAAC,YAAA;EAAA,MAAApB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAkB,WAAA,YAAAA,CAAA;IAAA,OAAApB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAa,WAAA;EAAA;AAAA;AAC7C,SAAAC,aAAA;EAAA,MAAArB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAmB,YAAA,YAAAA,CAAA;IAAA,OAAArB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAc,YAAA;EAAA;AAAA;AAC/C,SAAAC,WAAA;EAAA,MAAAtB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAoB,UAAA,YAAAA,CAAA;IAAA,OAAAtB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAe,UAAA;EAAA;AAAA;AAC3C,SAAAC,UAAA;EAAA,MAAAvB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAqB,SAAA,YAAAA,CAAA;IAAA,OAAAvB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAyCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAgB,SAAA;EAAA;AAAA;AACzC,SAAAC,OAAA;EAAA,MAAAxB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAsB,MAAA,YAAAA,CAAA;IAAA,OAAAxB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAiB,MAAA;EAAA;AAAA;AACnC,SAAAC,UAAA;EAAA,MAAAzB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAuB,SAAA,YAAAA,CAAA;IAAA,OAAAzB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAyCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAkB,SAAA;EAAA;AAAA;AACzC,SAAAC,QAAA;EAAA,MAAA1B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAwB,OAAA,YAAAA,CAAA;IAAA,OAAA1B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAmB,OAAA;EAAA;AAAA;AACrC,SAAAC,OAAA;EAAA,MAAA3B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAyB,MAAA,YAAAA,CAAA;IAAA,OAAA3B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAoB,MAAA;EAAA;AAAA;AACnC,SAAAC,QAAA;EAAA,MAAA5B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA0B,OAAA,YAAAA,CAAA;IAAA,OAAA5B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAqB,OAAA;EAAA;AAAA;AACrC,SAAAC,QAAA;EAAA,MAAA7B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA2B,OAAA,YAAAA,CAAA;IAAA,OAAA7B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAsB,OAAA;EAAA;AAAA;AACrC,SAAAC,oBAAA;EAAA,MAAA9B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA4B,mBAAA,YAAAA,CAAA;IAAA,OAAA9B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6DG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAuB,mBAAA;EAAA;AAAA;AAAA,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAA/B,wBAAA+B,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAA5B,GAAA,CAAAyB,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAtC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAuC,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAW,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAAtC,MAAA,CAAAuC,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAvC,GAAA,IAAAuC,CAAA,CAAAC,GAAA,IAAA5C,MAAA,CAAAC,cAAA,CAAAmC,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAF,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAY,GAAA,CAAAf,CAAA,EAAAO,CAAA,GAAAA,CAAA", "ignoreList": []}