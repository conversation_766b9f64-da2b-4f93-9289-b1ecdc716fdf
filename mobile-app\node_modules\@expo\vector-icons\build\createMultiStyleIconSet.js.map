{"version": 3, "file": "createMultiStyleIconSet.js", "sourceRoot": "", "sources": ["../src/createMultiStyleIconSet.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AAE7C,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAa5C,MAAM,CAAC,OAAO,UAAU,uBAAuB,CAAC,MAAkB,EAAE,YAAY,GAAG,EAAE;IACnF,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAEvC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IACxD,CAAC;IAED,MAAM,OAAO,GAAG;QACd,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC;QAC3B,cAAc,EAAE,CAAC,OAAY,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;QAC/C,cAAc,EAAE,CAAC,OAAY,EAAE,QAAa,EAAE,EAAE,CAAC,IAAI;QACrD,GAAG,YAAY;KAChB,CAAC;IAEF,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;QAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QAE3B,GAAG,CAAC,IAAI,CAAC,GAAG,aAAa,CACvB,KAAK,CAAC,QAAQ,IAAI,EAAE,EACpB,KAAK,CAAC,UAAU,IAAI,EAAE,EACtB,KAAK,CAAC,QAAQ,IAAI,EAAE,EACpB,KAAK,CAAC,SAAS,IAAI,EAAE,CACtB,CAAC;QAEF,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,SAAS,cAAc,CAAC,KAAK;QAC3B,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAC9B,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE,CACnB,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EACrF,OAAO,CAAC,YAAY,CACrB,CAAC;IACJ,CAAC;IAED,SAAS,kBAAkB,CAAC,KAAK;QAC/B,MAAM,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;QACvB,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QAEpC,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC;YAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEhE,MAAM,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE5C,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACtC,OAAO,OAAO,CAAC,YAAY,CAAC;QAC9B,CAAC;QAED,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED,SAAS,eAAe,CAAC,OAAO,EAAE,SAAS;QACzC,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IAC7D,CAAC;IAED,SAAS,WAAW,CAAC,KAAK;QACxB,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC7C,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACpC,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED,SAAS,gBAAgB,CAAC,KAAK,EAAE,IAAI,GAAG,EAAE;QACxC,IAAI,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrC,OAAO,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACxC,CAAC;QAED,OAAO,CAAC,IAAI;YACV,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7C,CAAC,CAAC,kBAAkB,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,SAAS,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC,YAAY;QACjD,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;IACjD,CAAC;IAED,SAAS,cAAc,CAAC,KAAK,GAAG,OAAO,CAAC,YAAY;QAClD,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,CAAC;IAClD,CAAC;IAED,SAAS,OAAO,CAAC,IAAI,EAAE,KAAK,GAAG,OAAO,CAAC,YAAY;QACjD,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,SAAS,qBAAqB,CAAC,WAAW,GAAG,EAAE;QAC7C,MAAM,SAAU,SAAQ,aAAa;YACnC,MAAM,CAAC,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBACpD,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBAClB,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC,CAAC;YAEP,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBACxD,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;gBACvC,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC,CAAC;YAEP,MAAM,CAAC,MAAM,CAAM;YAEnB,MAAM,CAAC,aAAa,GAAG,gBAAgB,CAAC;YACxC,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC;YACrC,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC;YACvC,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;YAEzB,MAAM;gBACJ,MAAM,eAAe,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvD,MAAM,iBAAiB,GAAG,eAAe,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;gBACxE,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEtC,OAAO,KAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YACvD,CAAC;;QAGH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,IAAI,GAAG,qBAAqB,EAAE,CAAC;IACrC,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAC9C,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import React, { PureComponent } from 'react';\n\nimport createIconSet from './createIconSet';\n\ntype FontStyle = {\n  fontFamily: string;\n  fontFile: any;\n  glyphMap: any;\n  fontStyle: any;\n};\n\ntype FontStyles = {\n  [key: string]: FontStyle;\n};\n\nexport default function createMultiStyleIconSet(styles: FontStyles, optionsInput = {}): any {\n  const styleNames = Object.keys(styles);\n\n  if (styleNames.length === 0) {\n    throw new Error('You need to add at least one style');\n  }\n\n  const options = {\n    defaultStyle: styleNames[0],\n    fallbackFamily: (_unused: any) => styleNames[0],\n    glyphValidator: (_unused: any, __unused: any) => true,\n    ...optionsInput,\n  };\n\n  const iconSets = styleNames.reduce((acc, name) => {\n    const style = styles[name];\n\n    acc[name] = createIconSet(\n      style.glyphMap || {},\n      style.fontFamily || '',\n      style.fontFile || '',\n      style.fontStyle || {}\n    );\n\n    return acc;\n  }, {});\n\n  function styleFromProps(props) {\n    return Object.keys(props).reduce(\n      (result, propName) =>\n        styleNames.indexOf(propName) !== -1 && props[propName] === true ? propName : result,\n      options.defaultStyle\n    );\n  }\n\n  function getIconSetForProps(props) {\n    const { name } = props;\n    const style = styleFromProps(props);\n\n    if (options.glyphValidator(name, style)) return iconSets[style];\n\n    const family = options.fallbackFamily(name);\n\n    if (styleNames.indexOf(family) === -1) {\n      return options.defaultStyle;\n    }\n\n    return iconSets[family];\n  }\n\n  function selectIconClass(iconSet, iconClass) {\n    return iconClass.length > 0 ? iconSet[iconClass] : iconSet;\n  }\n\n  function reduceProps(props) {\n    return Object.keys(props).reduce((acc, prop) => {\n      if (styleNames.indexOf(prop) === -1) {\n        acc[prop] = props[prop];\n      }\n\n      return acc;\n    }, {});\n  }\n\n  function getStyledIconSet(style, name = '') {\n    if (styleNames.indexOf(style) === -1) {\n      return iconSets[options.defaultStyle];\n    }\n\n    return !name\n      ? iconSets[styleFromProps({ [style]: true })]\n      : getIconSetForProps({ name, [style]: true });\n  }\n\n  function getFontFamily(style = options.defaultStyle) {\n    return getStyledIconSet(style).getFontFamily();\n  }\n\n  function getRawGlyphMap(style = options.defaultStyle) {\n    return getStyledIconSet(style).getRawGlyphMap();\n  }\n\n  function hasIcon(name, style = options.defaultStyle) {\n    return options.glyphValidator(name, style);\n  }\n\n  function createStyledIconClass(selectClass = '') {\n    class IconClass extends PureComponent {\n      static defaultProps = styleNames.reduce((acc, name) => {\n        acc[name] = false;\n        return acc;\n      }, {});\n\n      static font = Object.values(styles).reduce((acc, style) => {\n        acc[style.fontFamily] = style.fontFile;\n        return acc;\n      }, {});\n\n      static Button: any;\n\n      static StyledIconSet = getStyledIconSet;\n      static getFontFamily = getFontFamily;\n      static getRawGlyphMap = getRawGlyphMap;\n      static hasIcon = hasIcon;\n\n      render() {\n        const selectedIconSet = getIconSetForProps(this.props);\n        const SelectedIconClass = selectIconClass(selectedIconSet, selectClass);\n        const props = reduceProps(this.props);\n\n        return React.createElement(SelectedIconClass, props);\n      }\n    }\n\n    return IconClass;\n  }\n\n  const Icon = createStyledIconClass();\n  Icon.Button = createStyledIconClass('Button');\n  return Icon;\n}\n"]}