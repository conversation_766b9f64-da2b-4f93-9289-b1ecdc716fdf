const createExpoWebpackConfigAsync = require('@expo/webpack-config');

module.exports = async function (env, argv) {
  const config = await createExpoWebpackConfigAsync(
    {
      ...env,
      babel: {
        dangerouslyAddModulePathsToTranspile: [
          '@react-native-async-storage/async-storage',
          'react-native-paper',
          'react-native-vector-icons',
        ],
      },
    },
    argv
  );

  // Add fallbacks for Node.js modules
  config.resolve.fallback = {
    ...config.resolve.fallback,
    crypto: false,
    stream: false,
    buffer: false,
  };

  // Add alias for React Native web compatibility
  config.resolve.alias = {
    ...config.resolve.alias,
    'react-native$': 'react-native-web',
    'react-native/Libraries/EventEmitter/NativeEventEmitter':
      'react-native-web/dist/vendor/react-native/NativeEventEmitter',
    'react-native/Libraries/vendor/emitter/EventEmitter':
      'react-native-web/dist/vendor/react-native/emitter/EventEmitter',
  };

  return config;
};
