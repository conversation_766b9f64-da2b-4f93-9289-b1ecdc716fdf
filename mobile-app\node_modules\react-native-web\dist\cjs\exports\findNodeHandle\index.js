"use strict";

exports.__esModule = true;
exports.default = void 0;
/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */

var findNodeHandle = component => {
  throw new Error('findNodeHandle is not supported on web. ' + 'Use the ref property on the component instead.');
};
var _default = exports.default = findNodeHandle;
module.exports = exports.default;