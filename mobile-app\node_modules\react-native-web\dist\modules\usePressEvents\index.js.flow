/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

'use strict';

import type { EventHandlers, PressResponderConfig } from './PressResponder';
import PressResponder from './PressResponder';
import { useDebugValue, useEffect, useRef } from 'react';
declare export default function usePressEvents(hostRef: any, config: PressResponderConfig): EventHandlers;