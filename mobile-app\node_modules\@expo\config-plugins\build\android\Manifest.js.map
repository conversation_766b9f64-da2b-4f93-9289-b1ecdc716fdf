{"version": 3, "file": "Manifest.js", "names": ["_assert", "data", "_interopRequireDefault", "require", "_fs", "_path", "XML", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "writeAndroidManifestAsync", "manifestPath", "androidManifest", "manifestXml", "format", "fs", "promises", "mkdir", "path", "dirname", "recursive", "writeFile", "readAndroidManifestAsync", "xml", "readXMLAsync", "isManifest", "Error", "manifest", "getMainApplication", "application", "filter", "$", "endsWith", "getMainApplicationOrThrow", "mainApplication", "assert", "getMainActivityOrThrow", "mainActivity", "getMainActivity", "getRunnableActivity", "enabledActivities", "activity", "Array", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "action", "find", "category", "addMetaDataItemToMainApplication", "itemName", "itemValue", "itemType", "existingMetaDataItem", "newItem", "prefixAndroidKeys", "name", "length", "push", "removeMetaDataItemFromMainApplication", "index", "findMetaDataItem", "splice", "findApplicationSubItem", "parent", "findIndex", "findUsesLibraryItem", "getMainApplicationMetaDataValue", "item", "addUsesLibraryItemToMainApplication", "removeUsesLibraryItemFromMainApplication", "head", "entries", "reduce", "prev", "key", "curr", "ensureToolsAvailable", "ensureManifestHasNamespace", "namespace", "url"], "sources": ["../../src/android/Manifest.ts"], "sourcesContent": ["import assert from 'assert';\nimport fs from 'fs';\nimport path from 'path';\n\nimport * as XML from '../utils/XML';\n\nexport type StringBoolean = 'true' | 'false';\n\ntype ManifestMetaDataAttributes = AndroidManifestAttributes & {\n  'android:value'?: string;\n  'android:resource'?: string;\n};\n\ntype AndroidManifestAttributes = {\n  'android:name': string | 'android.intent.action.VIEW';\n  'tools:node'?: string | 'remove';\n};\n\ntype ManifestAction = {\n  $: AndroidManifestAttributes;\n};\n\ntype ManifestCategory = {\n  $: AndroidManifestAttributes;\n};\n\ntype ManifestData = {\n  $: {\n    [key: string]: string | undefined;\n    'android:host'?: string;\n    'android:pathPrefix'?: string;\n    'android:scheme'?: string;\n  };\n};\n\ntype ManifestReceiver = {\n  $: AndroidManifestAttributes & {\n    'android:exported'?: StringBoolean;\n    'android:enabled'?: StringBoolean;\n  };\n  'intent-filter'?: ManifestIntentFilter[];\n};\n\nexport type ManifestIntentFilter = {\n  $?: {\n    'android:autoVerify'?: StringBoolean;\n    'data-generated'?: StringBoolean;\n  };\n  action?: ManifestAction[];\n  data?: ManifestData[];\n  category?: ManifestCategory[];\n};\n\nexport type ManifestMetaData = {\n  $: ManifestMetaDataAttributes;\n};\n\ntype ManifestServiceAttributes = AndroidManifestAttributes & {\n  'android:enabled'?: StringBoolean;\n  'android:exported'?: StringBoolean;\n  'android:permission'?: string;\n  'android:foregroundServiceType'?: string;\n  // ...\n};\n\ntype ManifestService = {\n  $: ManifestServiceAttributes;\n  'intent-filter'?: ManifestIntentFilter[];\n};\n\ntype ManifestApplicationAttributes = {\n  'android:name': string | '.MainApplication';\n  'android:icon'?: string;\n  'android:roundIcon'?: string;\n  'android:label'?: string;\n  'android:allowBackup'?: StringBoolean;\n  'android:largeHeap'?: StringBoolean;\n  'android:requestLegacyExternalStorage'?: StringBoolean;\n  'android:supportsPictureInPicture'?: StringBoolean;\n  'android:usesCleartextTraffic'?: StringBoolean;\n  [key: string]: string | undefined;\n};\n\nexport type ManifestActivity = {\n  $: ManifestApplicationAttributes & {\n    'android:exported'?: StringBoolean;\n    'android:launchMode'?: string;\n    'android:theme'?: string;\n    'android:windowSoftInputMode'?:\n      | string\n      | 'stateUnspecified'\n      | 'stateUnchanged'\n      | 'stateHidden'\n      | 'stateAlwaysHidden'\n      | 'stateVisible'\n      | 'stateAlwaysVisible'\n      | 'adjustUnspecified'\n      | 'adjustResize'\n      | 'adjustPan';\n    [key: string]: string | undefined;\n  };\n  'intent-filter'?: ManifestIntentFilter[];\n  // ...\n};\n\nexport type ManifestUsesLibrary = {\n  $: AndroidManifestAttributes & {\n    'android:required'?: StringBoolean;\n  };\n};\n\nexport type ManifestApplication = {\n  $: ManifestApplicationAttributes;\n  activity?: ManifestActivity[];\n  service?: ManifestService[];\n  receiver?: ManifestReceiver[];\n  'meta-data'?: ManifestMetaData[];\n  'uses-library'?: ManifestUsesLibrary[];\n  // ...\n};\n\ntype ManifestPermission = {\n  $: AndroidManifestAttributes & {\n    'android:protectionLevel'?: string | 'signature';\n  };\n};\n\nexport type ManifestUsesPermission = {\n  $: AndroidManifestAttributes;\n};\n\ntype ManifestUsesFeature = {\n  $: AndroidManifestAttributes & {\n    'android:glEsVersion'?: string;\n    'android:required': StringBoolean;\n  };\n};\n\nexport type AndroidManifest = {\n  manifest: {\n    // Probably more, but this is currently all we'd need for most cases in Expo.\n    $: {\n      'xmlns:android': string;\n      'xmlns:tools'?: string;\n      package?: string;\n      [key: string]: string | undefined;\n    };\n    permission?: ManifestPermission[];\n    'uses-permission'?: ManifestUsesPermission[];\n    'uses-permission-sdk-23'?: ManifestUsesPermission[];\n    'uses-feature'?: ManifestUsesFeature[];\n    queries: ManifestQuery[];\n    application?: ManifestApplication[];\n  };\n};\n\ntype ManifestQueryIntent = Omit<ManifestIntentFilter, '$'>;\n\nexport type ManifestQuery = {\n  package?: {\n    $: {\n      'android:name': string;\n    };\n  }[];\n  intent?: ManifestQueryIntent[];\n  provider?: {\n    $: {\n      'android:authorities': string;\n    };\n  }[];\n};\n\nexport async function writeAndroidManifestAsync(\n  manifestPath: string,\n  androidManifest: AndroidManifest\n): Promise<void> {\n  const manifestXml = XML.format(androidManifest);\n  await fs.promises.mkdir(path.dirname(manifestPath), { recursive: true });\n  await fs.promises.writeFile(manifestPath, manifestXml);\n}\n\nexport async function readAndroidManifestAsync(manifestPath: string): Promise<AndroidManifest> {\n  const xml = await XML.readXMLAsync({ path: manifestPath });\n  if (!isManifest(xml)) {\n    throw new Error('Invalid manifest found at: ' + manifestPath);\n  }\n  return xml;\n}\n\nfunction isManifest(xml: XML.XMLObject): xml is AndroidManifest {\n  // TODO: Maybe more validation\n  return !!xml.manifest;\n}\n\n/** Returns the `manifest.application` tag ending in `.MainApplication` */\nexport function getMainApplication(androidManifest: AndroidManifest): ManifestApplication | null {\n  return (\n    androidManifest?.manifest?.application?.filter((e) =>\n      e?.$?.['android:name'].endsWith('.MainApplication')\n    )[0] ?? null\n  );\n}\n\nexport function getMainApplicationOrThrow(androidManifest: AndroidManifest): ManifestApplication {\n  const mainApplication = getMainApplication(androidManifest);\n  assert(mainApplication, 'AndroidManifest.xml is missing the required MainApplication element');\n  return mainApplication;\n}\n\nexport function getMainActivityOrThrow(androidManifest: AndroidManifest): ManifestActivity {\n  const mainActivity = getMainActivity(androidManifest);\n  assert(mainActivity, 'AndroidManifest.xml is missing the required MainActivity element');\n  return mainActivity;\n}\n\nexport function getRunnableActivity(androidManifest: AndroidManifest): ManifestActivity | null {\n  // Get enabled activities\n  const enabledActivities = androidManifest?.manifest?.application?.[0]?.activity?.filter?.(\n    (e: any) => e.$['android:enabled'] !== 'false' && e.$['android:enabled'] !== false\n  );\n\n  if (!enabledActivities) {\n    return null;\n  }\n\n  // Get the activity that has a runnable intent-filter\n  for (const activity of enabledActivities) {\n    if (Array.isArray(activity['intent-filter'])) {\n      for (const intentFilter of activity['intent-filter']) {\n        if (\n          intentFilter.action?.find(\n            (action) => action.$['android:name'] === 'android.intent.action.MAIN'\n          ) &&\n          intentFilter.category?.find(\n            (category) => category.$['android:name'] === 'android.intent.category.LAUNCHER'\n          )\n        ) {\n          return activity;\n        }\n      }\n    }\n  }\n\n  return null;\n}\n\nexport function getMainActivity(androidManifest: AndroidManifest): ManifestActivity | null {\n  const mainActivity = androidManifest?.manifest?.application?.[0]?.activity?.filter?.(\n    (e: any) => e.$['android:name'] === '.MainActivity'\n  );\n  return mainActivity?.[0] ?? null;\n}\n\nexport function addMetaDataItemToMainApplication(\n  mainApplication: ManifestApplication,\n  itemName: string,\n  itemValue: string,\n  itemType: 'resource' | 'value' = 'value'\n): ManifestApplication {\n  let existingMetaDataItem;\n  const newItem = {\n    $: prefixAndroidKeys({ name: itemName, [itemType]: itemValue }),\n  } as ManifestMetaData;\n  if (mainApplication['meta-data']) {\n    existingMetaDataItem = mainApplication['meta-data'].filter(\n      (e: any) => e.$['android:name'] === itemName\n    );\n    if (existingMetaDataItem.length) {\n      existingMetaDataItem[0].$[`android:${itemType}` as keyof ManifestMetaDataAttributes] =\n        itemValue;\n    } else {\n      mainApplication['meta-data'].push(newItem);\n    }\n  } else {\n    mainApplication['meta-data'] = [newItem];\n  }\n  return mainApplication;\n}\n\nexport function removeMetaDataItemFromMainApplication(mainApplication: any, itemName: string) {\n  const index = findMetaDataItem(mainApplication, itemName);\n  if (mainApplication?.['meta-data'] && index > -1) {\n    mainApplication['meta-data'].splice(index, 1);\n  }\n  return mainApplication;\n}\n\nfunction findApplicationSubItem(\n  mainApplication: ManifestApplication,\n  category: keyof ManifestApplication,\n  itemName: string\n): number {\n  const parent = mainApplication[category];\n  if (Array.isArray(parent)) {\n    const index = parent.findIndex((e: any) => e.$['android:name'] === itemName);\n\n    return index;\n  }\n  return -1;\n}\n\nexport function findMetaDataItem(mainApplication: any, itemName: string): number {\n  return findApplicationSubItem(mainApplication, 'meta-data', itemName);\n}\n\nexport function findUsesLibraryItem(mainApplication: any, itemName: string): number {\n  return findApplicationSubItem(mainApplication, 'uses-library', itemName);\n}\n\nexport function getMainApplicationMetaDataValue(\n  androidManifest: AndroidManifest,\n  name: string\n): string | null {\n  const mainApplication = getMainApplication(androidManifest);\n\n  if (mainApplication?.hasOwnProperty('meta-data')) {\n    const item = mainApplication?.['meta-data']?.find((e: any) => e.$['android:name'] === name);\n    return item?.$['android:value'] ?? null;\n  }\n\n  return null;\n}\n\nexport function addUsesLibraryItemToMainApplication(\n  mainApplication: ManifestApplication,\n  item: { name: string; required?: boolean }\n): ManifestApplication {\n  let existingMetaDataItem;\n  const newItem = {\n    $: prefixAndroidKeys(item),\n  } as ManifestUsesLibrary;\n\n  if (mainApplication['uses-library']) {\n    existingMetaDataItem = mainApplication['uses-library'].filter(\n      (e) => e.$['android:name'] === item.name\n    );\n    if (existingMetaDataItem.length) {\n      existingMetaDataItem[0].$ = newItem.$;\n    } else {\n      mainApplication['uses-library'].push(newItem);\n    }\n  } else {\n    mainApplication['uses-library'] = [newItem];\n  }\n  return mainApplication;\n}\n\nexport function removeUsesLibraryItemFromMainApplication(\n  mainApplication: ManifestApplication,\n  itemName: string\n) {\n  const index = findUsesLibraryItem(mainApplication, itemName);\n  if (mainApplication?.['uses-library'] && index > -1) {\n    mainApplication['uses-library'].splice(index, 1);\n  }\n  return mainApplication;\n}\n\nexport function prefixAndroidKeys<T extends Record<string, any> = Record<string, string>>(\n  head: T\n): Record<string, any> {\n  // prefix all keys with `android:`\n  return Object.entries(head).reduce(\n    (prev, [key, curr]) => ({ ...prev, [`android:${key}`]: curr }),\n    {} as T\n  );\n}\n\n/**\n * Ensure the `tools:*` namespace is available in the manifest.\n *\n * @param manifest AndroidManifest.xml\n * @returns manifest with the `tools:*` namespace available\n */\nexport function ensureToolsAvailable(manifest: AndroidManifest) {\n  return ensureManifestHasNamespace(manifest, {\n    namespace: 'xmlns:tools',\n    url: 'http://schemas.android.com/tools',\n  });\n}\n\n/**\n * Ensure a particular namespace is available in the manifest.\n *\n * @param manifest `AndroidManifest.xml`\n * @returns manifest with the provided namespace available\n */\nfunction ensureManifestHasNamespace(\n  manifest: AndroidManifest,\n  { namespace, url }: { namespace: string; url: string }\n) {\n  if (manifest?.manifest?.$?.[namespace]) {\n    return manifest;\n  }\n  manifest.manifest.$[namespace] = url;\n  return manifest;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,IAAA;EAAA,MAAAL,IAAA,GAAAM,uBAAA,CAAAJ,OAAA;EAAAG,GAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAoC,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAf,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAwK7B,eAAemB,yBAAyBA,CAC7CC,YAAoB,EACpBC,eAAgC,EACjB;EACf,MAAMC,WAAW,GAAGzB,GAAG,CAAD,CAAC,CAAC0B,MAAM,CAACF,eAAe,CAAC;EAC/C,MAAMG,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACC,eAAI,CAACC,OAAO,CAACR,YAAY,CAAC,EAAE;IAAES,SAAS,EAAE;EAAK,CAAC,CAAC;EACxE,MAAML,aAAE,CAACC,QAAQ,CAACK,SAAS,CAACV,YAAY,EAAEE,WAAW,CAAC;AACxD;AAEO,eAAeS,wBAAwBA,CAACX,YAAoB,EAA4B;EAC7F,MAAMY,GAAG,GAAG,MAAMnC,GAAG,CAAD,CAAC,CAACoC,YAAY,CAAC;IAAEN,IAAI,EAAEP;EAAa,CAAC,CAAC;EAC1D,IAAI,CAACc,UAAU,CAACF,GAAG,CAAC,EAAE;IACpB,MAAM,IAAIG,KAAK,CAAC,6BAA6B,GAAGf,YAAY,CAAC;EAC/D;EACA,OAAOY,GAAG;AACZ;AAEA,SAASE,UAAUA,CAACF,GAAkB,EAA0B;EAC9D;EACA,OAAO,CAAC,CAACA,GAAG,CAACI,QAAQ;AACvB;;AAEA;AACO,SAASC,kBAAkBA,CAAChB,eAAgC,EAA8B;EAC/F,OACEA,eAAe,EAAEe,QAAQ,EAAEE,WAAW,EAAEC,MAAM,CAAEvC,CAAC,IAC/CA,CAAC,EAAEwC,CAAC,GAAG,cAAc,CAAC,CAACC,QAAQ,CAAC,kBAAkB,CACpD,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AAEhB;AAEO,SAASC,yBAAyBA,CAACrB,eAAgC,EAAuB;EAC/F,MAAMsB,eAAe,GAAGN,kBAAkB,CAAChB,eAAe,CAAC;EAC3D,IAAAuB,iBAAM,EAACD,eAAe,EAAE,qEAAqE,CAAC;EAC9F,OAAOA,eAAe;AACxB;AAEO,SAASE,sBAAsBA,CAACxB,eAAgC,EAAoB;EACzF,MAAMyB,YAAY,GAAGC,eAAe,CAAC1B,eAAe,CAAC;EACrD,IAAAuB,iBAAM,EAACE,YAAY,EAAE,kEAAkE,CAAC;EACxF,OAAOA,YAAY;AACrB;AAEO,SAASE,mBAAmBA,CAAC3B,eAAgC,EAA2B;EAC7F;EACA,MAAM4B,iBAAiB,GAAG5B,eAAe,EAAEe,QAAQ,EAAEE,WAAW,GAAG,CAAC,CAAC,EAAEY,QAAQ,EAAEX,MAAM,GACpFvC,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,iBAAiB,CAAC,KAAK,OAAO,IAAIxC,CAAC,CAACwC,CAAC,CAAC,iBAAiB,CAAC,KAAK,KAC/E,CAAC;EAED,IAAI,CAACS,iBAAiB,EAAE;IACtB,OAAO,IAAI;EACb;;EAEA;EACA,KAAK,MAAMC,QAAQ,IAAID,iBAAiB,EAAE;IACxC,IAAIE,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,eAAe,CAAC,CAAC,EAAE;MAC5C,KAAK,MAAMG,YAAY,IAAIH,QAAQ,CAAC,eAAe,CAAC,EAAE;QACpD,IACEG,YAAY,CAACC,MAAM,EAAEC,IAAI,CACtBD,MAAM,IAAKA,MAAM,CAACd,CAAC,CAAC,cAAc,CAAC,KAAK,4BAC3C,CAAC,IACDa,YAAY,CAACG,QAAQ,EAAED,IAAI,CACxBC,QAAQ,IAAKA,QAAQ,CAAChB,CAAC,CAAC,cAAc,CAAC,KAAK,kCAC/C,CAAC,EACD;UACA,OAAOU,QAAQ;QACjB;MACF;IACF;EACF;EAEA,OAAO,IAAI;AACb;AAEO,SAASH,eAAeA,CAAC1B,eAAgC,EAA2B;EACzF,MAAMyB,YAAY,GAAGzB,eAAe,EAAEe,QAAQ,EAAEE,WAAW,GAAG,CAAC,CAAC,EAAEY,QAAQ,EAAEX,MAAM,GAC/EvC,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAK,eACtC,CAAC;EACD,OAAOM,YAAY,GAAG,CAAC,CAAC,IAAI,IAAI;AAClC;AAEO,SAASW,gCAAgCA,CAC9Cd,eAAoC,EACpCe,QAAgB,EAChBC,SAAiB,EACjBC,QAA8B,GAAG,OAAO,EACnB;EACrB,IAAIC,oBAAoB;EACxB,MAAMC,OAAO,GAAG;IACdtB,CAAC,EAAEuB,iBAAiB,CAAC;MAAEC,IAAI,EAAEN,QAAQ;MAAE,CAACE,QAAQ,GAAGD;IAAU,CAAC;EAChE,CAAqB;EACrB,IAAIhB,eAAe,CAAC,WAAW,CAAC,EAAE;IAChCkB,oBAAoB,GAAGlB,eAAe,CAAC,WAAW,CAAC,CAACJ,MAAM,CACvDvC,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAKkB,QACtC,CAAC;IACD,IAAIG,oBAAoB,CAACI,MAAM,EAAE;MAC/BJ,oBAAoB,CAAC,CAAC,CAAC,CAACrB,CAAC,CAAC,WAAWoB,QAAQ,EAAE,CAAqC,GAClFD,SAAS;IACb,CAAC,MAAM;MACLhB,eAAe,CAAC,WAAW,CAAC,CAACuB,IAAI,CAACJ,OAAO,CAAC;IAC5C;EACF,CAAC,MAAM;IACLnB,eAAe,CAAC,WAAW,CAAC,GAAG,CAACmB,OAAO,CAAC;EAC1C;EACA,OAAOnB,eAAe;AACxB;AAEO,SAASwB,qCAAqCA,CAACxB,eAAoB,EAAEe,QAAgB,EAAE;EAC5F,MAAMU,KAAK,GAAGC,gBAAgB,CAAC1B,eAAe,EAAEe,QAAQ,CAAC;EACzD,IAAIf,eAAe,GAAG,WAAW,CAAC,IAAIyB,KAAK,GAAG,CAAC,CAAC,EAAE;IAChDzB,eAAe,CAAC,WAAW,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAC/C;EACA,OAAOzB,eAAe;AACxB;AAEA,SAAS4B,sBAAsBA,CAC7B5B,eAAoC,EACpCa,QAAmC,EACnCE,QAAgB,EACR;EACR,MAAMc,MAAM,GAAG7B,eAAe,CAACa,QAAQ,CAAC;EACxC,IAAIL,KAAK,CAACC,OAAO,CAACoB,MAAM,CAAC,EAAE;IACzB,MAAMJ,KAAK,GAAGI,MAAM,CAACC,SAAS,CAAEzE,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAKkB,QAAQ,CAAC;IAE5E,OAAOU,KAAK;EACd;EACA,OAAO,CAAC,CAAC;AACX;AAEO,SAASC,gBAAgBA,CAAC1B,eAAoB,EAAEe,QAAgB,EAAU;EAC/E,OAAOa,sBAAsB,CAAC5B,eAAe,EAAE,WAAW,EAAEe,QAAQ,CAAC;AACvE;AAEO,SAASgB,mBAAmBA,CAAC/B,eAAoB,EAAEe,QAAgB,EAAU;EAClF,OAAOa,sBAAsB,CAAC5B,eAAe,EAAE,cAAc,EAAEe,QAAQ,CAAC;AAC1E;AAEO,SAASiB,+BAA+BA,CAC7CtD,eAAgC,EAChC2C,IAAY,EACG;EACf,MAAMrB,eAAe,GAAGN,kBAAkB,CAAChB,eAAe,CAAC;EAE3D,IAAIsB,eAAe,EAAE5B,cAAc,CAAC,WAAW,CAAC,EAAE;IAChD,MAAM6D,IAAI,GAAGjC,eAAe,GAAG,WAAW,CAAC,EAAEY,IAAI,CAAEvD,CAAM,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAKwB,IAAI,CAAC;IAC3F,OAAOY,IAAI,EAAEpC,CAAC,CAAC,eAAe,CAAC,IAAI,IAAI;EACzC;EAEA,OAAO,IAAI;AACb;AAEO,SAASqC,mCAAmCA,CACjDlC,eAAoC,EACpCiC,IAA0C,EACrB;EACrB,IAAIf,oBAAoB;EACxB,MAAMC,OAAO,GAAG;IACdtB,CAAC,EAAEuB,iBAAiB,CAACa,IAAI;EAC3B,CAAwB;EAExB,IAAIjC,eAAe,CAAC,cAAc,CAAC,EAAE;IACnCkB,oBAAoB,GAAGlB,eAAe,CAAC,cAAc,CAAC,CAACJ,MAAM,CAC1DvC,CAAC,IAAKA,CAAC,CAACwC,CAAC,CAAC,cAAc,CAAC,KAAKoC,IAAI,CAACZ,IACtC,CAAC;IACD,IAAIH,oBAAoB,CAACI,MAAM,EAAE;MAC/BJ,oBAAoB,CAAC,CAAC,CAAC,CAACrB,CAAC,GAAGsB,OAAO,CAACtB,CAAC;IACvC,CAAC,MAAM;MACLG,eAAe,CAAC,cAAc,CAAC,CAACuB,IAAI,CAACJ,OAAO,CAAC;IAC/C;EACF,CAAC,MAAM;IACLnB,eAAe,CAAC,cAAc,CAAC,GAAG,CAACmB,OAAO,CAAC;EAC7C;EACA,OAAOnB,eAAe;AACxB;AAEO,SAASmC,wCAAwCA,CACtDnC,eAAoC,EACpCe,QAAgB,EAChB;EACA,MAAMU,KAAK,GAAGM,mBAAmB,CAAC/B,eAAe,EAAEe,QAAQ,CAAC;EAC5D,IAAIf,eAAe,GAAG,cAAc,CAAC,IAAIyB,KAAK,GAAG,CAAC,CAAC,EAAE;IACnDzB,eAAe,CAAC,cAAc,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EAClD;EACA,OAAOzB,eAAe;AACxB;AAEO,SAASoB,iBAAiBA,CAC/BgB,IAAO,EACc;EACrB;EACA,OAAOpE,MAAM,CAACqE,OAAO,CAACD,IAAI,CAAC,CAACE,MAAM,CAChC,CAACC,IAAI,EAAE,CAACC,GAAG,EAAEC,IAAI,CAAC,MAAM;IAAE,GAAGF,IAAI;IAAE,CAAC,WAAWC,GAAG,EAAE,GAAGC;EAAK,CAAC,CAAC,EAC9D,CAAC,CACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,oBAAoBA,CAACjD,QAAyB,EAAE;EAC9D,OAAOkD,0BAA0B,CAAClD,QAAQ,EAAE;IAC1CmD,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE;EACP,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,0BAA0BA,CACjClD,QAAyB,EACzB;EAAEmD,SAAS;EAAEC;AAAwC,CAAC,EACtD;EACA,IAAIpD,QAAQ,EAAEA,QAAQ,EAAEI,CAAC,GAAG+C,SAAS,CAAC,EAAE;IACtC,OAAOnD,QAAQ;EACjB;EACAA,QAAQ,CAACA,QAAQ,CAACI,CAAC,CAAC+C,SAAS,CAAC,GAAGC,GAAG;EACpC,OAAOpD,QAAQ;AACjB", "ignoreList": []}