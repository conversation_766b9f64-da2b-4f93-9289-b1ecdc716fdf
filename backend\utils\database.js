const mongoose = require('mongoose');
const { User, Report, AuditLog } = require('../models');

/**
 * Connect to MongoDB database
 */
const connectDB = async () => {
  try {
    // Try MongoDB Atlas first, then local MongoDB
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/police-reporting';

    const conn = await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    console.log('💡 Tip: Make sure MongoDB is running or use MongoDB Atlas');
    console.log('   Local: mongod --dbpath ./data/db');
    console.log('   Atlas: Set MONGODB_URI environment variable');

    // Don't exit in development, use in-memory fallback
    if (process.env.NODE_ENV !== 'production') {
      console.log('🔄 Continuing without database for development...');
      return null;
    }
    process.exit(1);
  }
};

/**
 * Disconnect from MongoDB
 */
const disconnectDB = async () => {
  try {
    await mongoose.connection.close();
    console.log('✅ MongoDB Disconnected');
  } catch (error) {
    console.error('❌ Error disconnecting from database:', error);
  }
};

/**
 * Seed initial data
 */
const seedDatabase = async () => {
  try {
    console.log('🌱 Seeding database...');

    // Check if users already exist
    const existingUsers = await User.countDocuments();
    if (existingUsers > 0) {
      console.log('📊 Database already seeded');
      return;
    }

    // Create default users
    const defaultUsers = [
      {
        badgeNumber: 'ADMIN001',
        firstName: 'System',
        lastName: 'Administrator',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        department: 'Administration',
        rank: 'Administrator',
        station: 'Headquarters'
      },
      {
        badgeNumber: 'CAP001',
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>',
        password: 'captain123',
        role: 'captain',
        department: 'Criminal Investigation',
        rank: 'Captain',
        station: 'Central Station'
      },
      {
        badgeNumber: 'SUP001',
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        password: 'supervisor123',
        role: 'supervisor',
        department: 'Patrol',
        rank: 'Sergeant',
        station: 'North Station'
      },
      {
        badgeNumber: 'OFF001',
        firstName: 'Mike',
        lastName: 'Davis',
        email: '<EMAIL>',
        role: 'officer',
        department: 'Patrol',
        rank: 'Officer',
        station: 'North Station'
      },
      {
        badgeNumber: 'OFF002',
        firstName: 'Lisa',
        lastName: 'Wilson',
        email: '<EMAIL>',
        role: 'officer',
        department: 'Traffic',
        rank: 'Officer',
        station: 'South Station'
      }
    ];

    await User.insertMany(defaultUsers);
    console.log('✅ Default users created');

    // Create sample reports
    const officers = await User.find({ role: 'officer' });
    if (officers.length > 0) {
      const sampleReports = [
        {
          officerId: officers[0]._id,
          officerBadge: officers[0].badgeNumber,
          incidentType: 'theft',
          title: 'Bicycle Theft at City Park',
          description: 'Victim reported bicycle stolen from bike rack at City Park. Blue mountain bike, brand Trek, estimated value $500.',
          location: {
            address: '123 Park Avenue, City Center',
            coordinates: {
              latitude: 40.7128,
              longitude: -74.0060
            },
            district: 'Central'
          },
          dateTime: {
            occurred: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
            reported: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
          },
          status: 'pending',
          priority: 'medium'
        },
        {
          officerId: officers[1] ? officers[1]._id : officers[0]._id,
          officerBadge: officers[1] ? officers[1].badgeNumber : officers[0].badgeNumber,
          incidentType: 'accident',
          title: 'Vehicle Collision on Main Street',
          description: 'Two-vehicle collision at intersection of Main Street and Oak Avenue. Minor injuries reported.',
          location: {
            address: 'Main Street & Oak Avenue',
            coordinates: {
              latitude: 40.7589,
              longitude: -73.9851
            },
            district: 'Downtown'
          },
          dateTime: {
            occurred: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
            reported: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
          },
          status: 'in_progress',
          priority: 'high'
        }
      ];

      await Report.insertMany(sampleReports);
      console.log('✅ Sample reports created');
    }

    console.log('🎉 Database seeding completed');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
};

/**
 * Clear all data from database (for testing)
 */
const clearDatabase = async () => {
  try {
    await User.deleteMany({});
    await Report.deleteMany({});
    await AuditLog.deleteMany({});
    console.log('🗑️ Database cleared');
  } catch (error) {
    console.error('❌ Error clearing database:', error);
  }
};

module.exports = {
  connectDB,
  disconnectDB,
  seedDatabase,
  clearDatabase
};
