import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = 'http://localhost:5000/api'; // Change this to your backend URL

// Mock data for development when backend is not available
const MOCK_USERS = {
  '12345': {
    id: '1',
    badgeNumber: '12345',
    firstName: '<PERSON>',
    lastName: 'Doe',
    role: 'officer',
    department: 'Patrol',
    station: 'North Station'
  },
  '67890': {
    id: '2',
    badgeNumber: '67890',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    role: 'supervisor',
    department: 'Criminal Investigation',
    station: 'Central Station'
  }
};

class AuthService {
  async login(badgeNumber, password = '') {
    try {
      // Try API first
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          badgeNumber,
          password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Store token and user data
        await AsyncStorage.setItem('userToken', data.token);
        await AsyncStorage.setItem('userData', JSON.stringify(data.user));

        return {
          success: true,
          token: data.token,
          user: data.user,
        };
      } else {
        return {
          success: false,
          message: data.message || 'Login failed',
        };
      }
    } catch (error) {
      console.error('Auth service login error:', error);

      // Fallback to mock authentication for development
      console.log('🔄 Using mock authentication for development');

      if (MOCK_USERS[badgeNumber]) {
        const user = MOCK_USERS[badgeNumber];
        const mockToken = `mock_token_${badgeNumber}_${Date.now()}`;

        // Store mock data
        await AsyncStorage.setItem('userToken', mockToken);
        await AsyncStorage.setItem('userData', JSON.stringify(user));

        return {
          success: true,
          token: mockToken,
          user: user,
        };
      }

      return {
        success: false,
        message: 'Invalid badge number. Try 12345 or 67890 for demo.',
      };
    }
  }

  async logout() {
    try {
      // Get token for logout request
      const token = await AsyncStorage.getItem('userToken');
      
      if (token) {
        // Call logout endpoint
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }

      // Clear local storage
      await AsyncStorage.multiRemove(['userToken', 'userData']);
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local storage even if API call fails
      await AsyncStorage.multiRemove(['userToken', 'userData']);
      return { success: true };
    }
  }

  async getCurrentUser() {
    try {
      const token = await AsyncStorage.getItem('userToken');
      const userData = await AsyncStorage.getItem('userData');

      if (token && userData) {
        return {
          token,
          user: JSON.parse(userData),
        };
      }

      return null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  async isAuthenticated() {
    try {
      const token = await AsyncStorage.getItem('userToken');
      return !!token;
    } catch (error) {
      console.error('Check authentication error:', error);
      return false;
    }
  }

  async getAuthHeaders() {
    try {
      const token = await AsyncStorage.getItem('userToken');
      if (token) {
        return {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        };
      }
      return {
        'Content-Type': 'application/json',
      };
    } catch (error) {
      console.error('Get auth headers error:', error);
      return {
        'Content-Type': 'application/json',
      };
    }
  }
}

export const authService = new AuthService();
