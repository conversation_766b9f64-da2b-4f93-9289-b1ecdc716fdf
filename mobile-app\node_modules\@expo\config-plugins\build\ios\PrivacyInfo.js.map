{"version": 3, "file": "PrivacyInfo.js", "names": ["_plist", "data", "_interopRequireDefault", "require", "_fs", "_path", "_Xcodeproj", "_iosPlugins", "e", "__esModule", "default", "withPrivacyInfo", "config", "privacyManifests", "ios", "withXcodeProject", "projectConfig", "setPrivacyInfo", "projectRoot", "platformProjectRoot", "modRequest", "projectName", "getProjectName", "privacyFilePath", "path", "join", "existingFileContent", "getFileContents", "parsed<PERSON><PERSON><PERSON>", "plist", "parse", "mergedContent", "mergePrivacyInfo", "contents", "build", "ensureFileExists", "modResults", "hasFile", "addResourceFileToGroup", "filepath", "groupName", "project", "isBuildFile", "verbose", "filePath", "fs", "existsSync", "readFileSync", "encoding", "dirname", "mkdirSync", "recursive", "writeFileSync", "existing", "NSPrivacyAccessedAPITypes", "NSPrivacyCollectedDataTypes", "NSPrivacyTracking", "NSPrivacyTrackingDomains", "structuredClone", "for<PERSON>ach", "newType", "existingType", "find", "t", "NSPrivacyAccessedAPIType", "push", "NSPrivacyAccessedAPITypeReasons", "Set", "concat", "NSPrivacyCollectedDataType", "NSPrivacyCollectedDataTypePurposes"], "sources": ["../../src/ios/PrivacyInfo.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport plist from '@expo/plist';\nimport fs from 'fs';\nimport path from 'path';\nimport type { XcodeProject } from 'xcode';\n\nimport { ExportedConfigWithProps } from '../Plugin.types';\nimport { addResourceFileToGroup, getProjectName } from './utils/Xcodeproj';\nimport { withXcodeProject } from '../plugins/ios-plugins';\n\nexport type PrivacyInfo = {\n  NSPrivacyAccessedAPITypes: {\n    NSPrivacyAccessedAPIType: string;\n    NSPrivacyAccessedAPITypeReasons: string[];\n  }[];\n  NSPrivacyCollectedDataTypes: {\n    NSPrivacyCollectedDataType: string;\n    NSPrivacyCollectedDataTypeLinked: boolean;\n    NSPrivacyCollectedDataTypeTracking: boolean;\n    NSPrivacyCollectedDataTypePurposes: string[];\n  }[];\n  NSPrivacyTracking: boolean;\n  NSPrivacyTrackingDomains: string[];\n};\n\nexport function withPrivacyInfo(config: ExpoConfig): ExpoConfig {\n  const privacyManifests = config.ios?.privacyManifests;\n  if (!privacyManifests) {\n    return config;\n  }\n\n  return withXcodeProject(config, (projectConfig: ExportedConfigWithProps<XcodeProject>) => {\n    return setPrivacyInfo(projectConfig, privacyManifests);\n  });\n}\n\nexport function setPrivacyInfo(\n  projectConfig: ExportedConfigWithProps<XcodeProject>,\n  privacyManifests: Partial<PrivacyInfo>\n) {\n  const { projectRoot, platformProjectRoot } = projectConfig.modRequest;\n\n  const projectName = getProjectName(projectRoot);\n\n  const privacyFilePath = path.join(platformProjectRoot, projectName, 'PrivacyInfo.xcprivacy');\n\n  const existingFileContent = getFileContents(privacyFilePath);\n\n  const parsedContent = existingFileContent ? plist.parse(existingFileContent) : {};\n  const mergedContent = mergePrivacyInfo(parsedContent, privacyManifests);\n  const contents = plist.build(mergedContent);\n\n  ensureFileExists(privacyFilePath, contents);\n\n  if (!projectConfig.modResults.hasFile(privacyFilePath)) {\n    projectConfig.modResults = addResourceFileToGroup({\n      filepath: path.join(projectName, 'PrivacyInfo.xcprivacy'),\n      groupName: projectName,\n      project: projectConfig.modResults,\n      isBuildFile: true,\n      verbose: true,\n    });\n  }\n\n  return projectConfig;\n}\n\nfunction getFileContents(filePath: string): string | null {\n  if (!fs.existsSync(filePath)) {\n    return null;\n  }\n  return fs.readFileSync(filePath, { encoding: 'utf8' });\n}\n\nfunction ensureFileExists(filePath: string, contents: string) {\n  if (!fs.existsSync(path.dirname(filePath))) {\n    fs.mkdirSync(path.dirname(filePath), { recursive: true });\n  }\n  fs.writeFileSync(filePath, contents);\n}\n\nexport function mergePrivacyInfo(\n  existing: Partial<PrivacyInfo>,\n  privacyManifests: Partial<PrivacyInfo>\n): PrivacyInfo {\n  let {\n    NSPrivacyAccessedAPITypes = [],\n    NSPrivacyCollectedDataTypes = [],\n    NSPrivacyTracking = false,\n    NSPrivacyTrackingDomains = [],\n  } = structuredClone(existing);\n  // tracking is a boolean, so we can just overwrite it\n  NSPrivacyTracking = privacyManifests.NSPrivacyTracking ?? existing.NSPrivacyTracking ?? false;\n  // merge the api types – for each type ensure the key is in the array, and if it is add the reason if it's not there\n  privacyManifests.NSPrivacyAccessedAPITypes?.forEach((newType) => {\n    const existingType = NSPrivacyAccessedAPITypes.find(\n      (t) => t.NSPrivacyAccessedAPIType === newType.NSPrivacyAccessedAPIType\n    );\n    if (!existingType) {\n      NSPrivacyAccessedAPITypes.push(newType);\n    } else {\n      existingType.NSPrivacyAccessedAPITypeReasons = [\n        ...new Set(\n          existingType?.NSPrivacyAccessedAPITypeReasons?.concat(\n            ...newType.NSPrivacyAccessedAPITypeReasons\n          )\n        ),\n      ];\n    }\n  });\n  // merge the collected data types – for each type ensure the key is in the array, and if it is add the purposes if it's not there\n  privacyManifests.NSPrivacyCollectedDataTypes?.forEach((newType) => {\n    const existingType = NSPrivacyCollectedDataTypes.find(\n      (t) => t.NSPrivacyCollectedDataType === newType.NSPrivacyCollectedDataType\n    );\n    if (!existingType) {\n      NSPrivacyCollectedDataTypes.push(newType);\n    } else {\n      existingType.NSPrivacyCollectedDataTypePurposes = [\n        ...new Set(\n          existingType?.NSPrivacyCollectedDataTypePurposes?.concat(\n            ...newType.NSPrivacyCollectedDataTypePurposes\n          )\n        ),\n      ];\n    }\n  });\n  // merge the tracking domains\n  NSPrivacyTrackingDomains = [\n    ...new Set(NSPrivacyTrackingDomains.concat(privacyManifests.NSPrivacyTrackingDomains ?? [])),\n  ];\n\n  return {\n    NSPrivacyAccessedAPITypes,\n    NSPrivacyCollectedDataTypes,\n    NSPrivacyTracking,\n    NSPrivacyTrackingDomains,\n  };\n}\n"], "mappings": ";;;;;;;;AACA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,IAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,MAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,KAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,SAAAK,WAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,UAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,YAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,WAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0D,SAAAC,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAiBnD,SAASG,eAAeA,CAACC,MAAkB,EAAc;EAC9D,MAAMC,gBAAgB,GAAGD,MAAM,CAACE,GAAG,EAAED,gBAAgB;EACrD,IAAI,CAACA,gBAAgB,EAAE;IACrB,OAAOD,MAAM;EACf;EAEA,OAAO,IAAAG,8BAAgB,EAACH,MAAM,EAAGI,aAAoD,IAAK;IACxF,OAAOC,cAAc,CAACD,aAAa,EAAEH,gBAAgB,CAAC;EACxD,CAAC,CAAC;AACJ;AAEO,SAASI,cAAcA,CAC5BD,aAAoD,EACpDH,gBAAsC,EACtC;EACA,MAAM;IAAEK,WAAW;IAAEC;EAAoB,CAAC,GAAGH,aAAa,CAACI,UAAU;EAErE,MAAMC,WAAW,GAAG,IAAAC,2BAAc,EAACJ,WAAW,CAAC;EAE/C,MAAMK,eAAe,GAAGC,eAAI,CAACC,IAAI,CAACN,mBAAmB,EAAEE,WAAW,EAAE,uBAAuB,CAAC;EAE5F,MAAMK,mBAAmB,GAAGC,eAAe,CAACJ,eAAe,CAAC;EAE5D,MAAMK,aAAa,GAAGF,mBAAmB,GAAGG,gBAAK,CAACC,KAAK,CAACJ,mBAAmB,CAAC,GAAG,CAAC,CAAC;EACjF,MAAMK,aAAa,GAAGC,gBAAgB,CAACJ,aAAa,EAAEf,gBAAgB,CAAC;EACvE,MAAMoB,QAAQ,GAAGJ,gBAAK,CAACK,KAAK,CAACH,aAAa,CAAC;EAE3CI,gBAAgB,CAACZ,eAAe,EAAEU,QAAQ,CAAC;EAE3C,IAAI,CAACjB,aAAa,CAACoB,UAAU,CAACC,OAAO,CAACd,eAAe,CAAC,EAAE;IACtDP,aAAa,CAACoB,UAAU,GAAG,IAAAE,mCAAsB,EAAC;MAChDC,QAAQ,EAAEf,eAAI,CAACC,IAAI,CAACJ,WAAW,EAAE,uBAAuB,CAAC;MACzDmB,SAAS,EAAEnB,WAAW;MACtBoB,OAAO,EAAEzB,aAAa,CAACoB,UAAU;MACjCM,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EAEA,OAAO3B,aAAa;AACtB;AAEA,SAASW,eAAeA,CAACiB,QAAgB,EAAiB;EACxD,IAAI,CAACC,aAAE,CAACC,UAAU,CAACF,QAAQ,CAAC,EAAE;IAC5B,OAAO,IAAI;EACb;EACA,OAAOC,aAAE,CAACE,YAAY,CAACH,QAAQ,EAAE;IAAEI,QAAQ,EAAE;EAAO,CAAC,CAAC;AACxD;AAEA,SAASb,gBAAgBA,CAACS,QAAgB,EAAEX,QAAgB,EAAE;EAC5D,IAAI,CAACY,aAAE,CAACC,UAAU,CAACtB,eAAI,CAACyB,OAAO,CAACL,QAAQ,CAAC,CAAC,EAAE;IAC1CC,aAAE,CAACK,SAAS,CAAC1B,eAAI,CAACyB,OAAO,CAACL,QAAQ,CAAC,EAAE;MAAEO,SAAS,EAAE;IAAK,CAAC,CAAC;EAC3D;EACAN,aAAE,CAACO,aAAa,CAACR,QAAQ,EAAEX,QAAQ,CAAC;AACtC;AAEO,SAASD,gBAAgBA,CAC9BqB,QAA8B,EAC9BxC,gBAAsC,EACzB;EACb,IAAI;IACFyC,yBAAyB,GAAG,EAAE;IAC9BC,2BAA2B,GAAG,EAAE;IAChCC,iBAAiB,GAAG,KAAK;IACzBC,wBAAwB,GAAG;EAC7B,CAAC,GAAGC,eAAe,CAACL,QAAQ,CAAC;EAC7B;EACAG,iBAAiB,GAAG3C,gBAAgB,CAAC2C,iBAAiB,IAAIH,QAAQ,CAACG,iBAAiB,IAAI,KAAK;EAC7F;EACA3C,gBAAgB,CAACyC,yBAAyB,EAAEK,OAAO,CAAEC,OAAO,IAAK;IAC/D,MAAMC,YAAY,GAAGP,yBAAyB,CAACQ,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACC,wBAAwB,KAAKJ,OAAO,CAACI,wBAChD,CAAC;IACD,IAAI,CAACH,YAAY,EAAE;MACjBP,yBAAyB,CAACW,IAAI,CAACL,OAAO,CAAC;IACzC,CAAC,MAAM;MACLC,YAAY,CAACK,+BAA+B,GAAG,CAC7C,GAAG,IAAIC,GAAG,CACRN,YAAY,EAAEK,+BAA+B,EAAEE,MAAM,CACnD,GAAGR,OAAO,CAACM,+BACb,CACF,CAAC,CACF;IACH;EACF,CAAC,CAAC;EACF;EACArD,gBAAgB,CAAC0C,2BAA2B,EAAEI,OAAO,CAAEC,OAAO,IAAK;IACjE,MAAMC,YAAY,GAAGN,2BAA2B,CAACO,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACM,0BAA0B,KAAKT,OAAO,CAACS,0BAClD,CAAC;IACD,IAAI,CAACR,YAAY,EAAE;MACjBN,2BAA2B,CAACU,IAAI,CAACL,OAAO,CAAC;IAC3C,CAAC,MAAM;MACLC,YAAY,CAACS,kCAAkC,GAAG,CAChD,GAAG,IAAIH,GAAG,CACRN,YAAY,EAAES,kCAAkC,EAAEF,MAAM,CACtD,GAAGR,OAAO,CAACU,kCACb,CACF,CAAC,CACF;IACH;EACF,CAAC,CAAC;EACF;EACAb,wBAAwB,GAAG,CACzB,GAAG,IAAIU,GAAG,CAACV,wBAAwB,CAACW,MAAM,CAACvD,gBAAgB,CAAC4C,wBAAwB,IAAI,EAAE,CAAC,CAAC,CAC7F;EAED,OAAO;IACLH,yBAAyB;IACzBC,2BAA2B;IAC3BC,iBAAiB;IACjBC;EACF,CAAC;AACH", "ignoreList": []}