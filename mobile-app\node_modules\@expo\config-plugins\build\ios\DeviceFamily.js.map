{"version": 3, "file": "DeviceFamily.js", "names": ["_iosPlugins", "data", "require", "_warnings", "withDeviceFamily", "config", "withXcodeProject", "modResults", "setDeviceFamily", "project", "exports", "getSupportsTablet", "ios", "supportsTablet", "getIsTabletOnly", "isTabletOnly", "getDeviceFamilies", "addWarningIOS", "formatDeviceFamilies", "deviceFamilies", "join", "configurations", "pbxXCBuildConfigurationSection", "buildSettings", "Object", "values", "PRODUCT_NAME", "TVOS_DEPLOYMENT_TARGET", "TARGETED_DEVICE_FAMILY"], "sources": ["../../src/ios/DeviceFamily.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport { XcodeProject } from 'xcode';\n\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withXcodeProject } from '../plugins/ios-plugins';\nimport { addWarningIOS } from '../utils/warnings';\n\nexport const withDeviceFamily: ConfigPlugin = (config) => {\n  return withXcodeProject(config, async (config) => {\n    config.modResults = await setDeviceFamily(config, {\n      project: config.modResults,\n    });\n    return config;\n  });\n};\n\nexport function getSupportsTablet(config: Pick<ExpoConfig, 'ios'>): boolean {\n  return !!config.ios?.supportsTablet;\n}\n\nexport function getIsTabletOnly(config: Pick<ExpoConfig, 'ios'>): boolean {\n  return !!config?.ios?.isTabletOnly;\n}\n\nexport function getDeviceFamilies(config: Pick<ExpoConfig, 'ios'>): number[] {\n  const supportsTablet = getSupportsTablet(config);\n  const isTabletOnly = getIsTabletOnly(config);\n\n  if (isTabletOnly && config.ios?.supportsTablet === false) {\n    addWarningIOS(\n      'ios.supportsTablet',\n      `Found contradictory values: \\`{ ios: { isTabletOnly: true, supportsTablet: false } }\\`. Using \\`{ isTabletOnly: true }\\`.`\n    );\n  }\n\n  // 1 is iPhone, 2 is iPad\n  if (isTabletOnly) {\n    return [2];\n  } else if (supportsTablet) {\n    return [1, 2];\n  } else {\n    // is iPhone only\n    return [1];\n  }\n}\n\n/**\n * Wrapping the families in double quotes is the only way to set a value with a comma in it.\n *\n * @param deviceFamilies\n */\nexport function formatDeviceFamilies(deviceFamilies: number[]): string {\n  return `\"${deviceFamilies.join(',')}\"`;\n}\n\n/**\n * Add to pbxproj under TARGETED_DEVICE_FAMILY\n */\nexport function setDeviceFamily(\n  config: Pick<ExpoConfig, 'ios'>,\n  { project }: { project: XcodeProject }\n): XcodeProject {\n  const deviceFamilies = formatDeviceFamilies(getDeviceFamilies(config));\n\n  const configurations = project.pbxXCBuildConfigurationSection();\n  // @ts-ignore\n  for (const { buildSettings } of Object.values(configurations || {})) {\n    // Guessing that this is the best way to emulate Xcode.\n    // Using `project.addToBuildSettings` modifies too many targets.\n    if (typeof buildSettings?.PRODUCT_NAME !== 'undefined') {\n      if (typeof buildSettings?.TVOS_DEPLOYMENT_TARGET !== 'undefined') {\n        buildSettings.TARGETED_DEVICE_FAMILY = '3';\n      } else {\n        buildSettings.TARGETED_DEVICE_FAMILY = deviceFamilies;\n      }\n    }\n  }\n\n  return project;\n}\n"], "mappings": ";;;;;;;;;;;AAIA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,gBAA8B,GAAIC,MAAM,IAAK;EACxD,OAAO,IAAAC,8BAAgB,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IAChDA,MAAM,CAACE,UAAU,GAAG,MAAMC,eAAe,CAACH,MAAM,EAAE;MAChDI,OAAO,EAAEJ,MAAM,CAACE;IAClB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAN,gBAAA,GAAAA,gBAAA;AAEK,SAASO,iBAAiBA,CAACN,MAA+B,EAAW;EAC1E,OAAO,CAAC,CAACA,MAAM,CAACO,GAAG,EAAEC,cAAc;AACrC;AAEO,SAASC,eAAeA,CAACT,MAA+B,EAAW;EACxE,OAAO,CAAC,CAACA,MAAM,EAAEO,GAAG,EAAEG,YAAY;AACpC;AAEO,SAASC,iBAAiBA,CAACX,MAA+B,EAAY;EAC3E,MAAMQ,cAAc,GAAGF,iBAAiB,CAACN,MAAM,CAAC;EAChD,MAAMU,YAAY,GAAGD,eAAe,CAACT,MAAM,CAAC;EAE5C,IAAIU,YAAY,IAAIV,MAAM,CAACO,GAAG,EAAEC,cAAc,KAAK,KAAK,EAAE;IACxD,IAAAI,yBAAa,EACX,oBAAoB,EACpB,2HACF,CAAC;EACH;;EAEA;EACA,IAAIF,YAAY,EAAE;IAChB,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC,MAAM,IAAIF,cAAc,EAAE;IACzB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACf,CAAC,MAAM;IACL;IACA,OAAO,CAAC,CAAC,CAAC;EACZ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASK,oBAAoBA,CAACC,cAAwB,EAAU;EACrE,OAAO,IAAIA,cAAc,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG;AACxC;;AAEA;AACA;AACA;AACO,SAASZ,eAAeA,CAC7BH,MAA+B,EAC/B;EAAEI;AAAmC,CAAC,EACxB;EACd,MAAMU,cAAc,GAAGD,oBAAoB,CAACF,iBAAiB,CAACX,MAAM,CAAC,CAAC;EAEtE,MAAMgB,cAAc,GAAGZ,OAAO,CAACa,8BAA8B,CAAC,CAAC;EAC/D;EACA,KAAK,MAAM;IAAEC;EAAc,CAAC,IAAIC,MAAM,CAACC,MAAM,CAACJ,cAAc,IAAI,CAAC,CAAC,CAAC,EAAE;IACnE;IACA;IACA,IAAI,OAAOE,aAAa,EAAEG,YAAY,KAAK,WAAW,EAAE;MACtD,IAAI,OAAOH,aAAa,EAAEI,sBAAsB,KAAK,WAAW,EAAE;QAChEJ,aAAa,CAACK,sBAAsB,GAAG,GAAG;MAC5C,CAAC,MAAM;QACLL,aAAa,CAACK,sBAAsB,GAAGT,cAAc;MACvD;IACF;EACF;EAEA,OAAOV,OAAO;AAChB", "ignoreList": []}