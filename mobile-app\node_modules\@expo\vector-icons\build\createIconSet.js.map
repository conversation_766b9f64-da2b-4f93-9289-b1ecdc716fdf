{"version": 3, "file": "createIconSet.js", "sourceRoot": "", "sources": ["../src/createIconSet.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,OAAO,KAAyB,MAAM,OAAO,CAAC;AAC9C,OAAO,EACL,IAAI,GAOL,MAAM,cAAc,CAAC;AAEtB,OAAO,aAAa,MAAM,wDAAwD,CAAC;AACnF,OAAO,yBAAyB,MAAM,oDAAoD,CAAC;AAE3F,OAAO,EACL,kBAAkB,EAClB,iBAAiB,GAClB,MAAM,wDAAwD,CAAC;AAoFhE,MAAM,CAAC,OAAO,WACZ,QAAqB,EACrB,QAAY,EACZ,WAAW,EACX,SAAe;IAEf,MAAM,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,WAAW,EAAE,CAAC;IACzC,MAAM,gBAAgB,GAAG,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IAE5E,OAAO,MAAM,IAAK,SAAQ,KAAK,CAAC,SAAuB;QACrD,MAAM,CAAC,YAAY,GAAG,gBAAgB,CAAC,YAAY,CAAC;QACpD,MAAM,CAAC,MAAM,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC3B,MAAM,CAAC,cAAc,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC;QACvC,MAAM,CAAC,aAAa,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC;QACtC,MAAM,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QAEnB,QAAQ,GAAG,KAAK,CAAC;QACjB,KAAK,CAAO;QAEZ,KAAK,GAAG;YACN,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;SACtC,CAAC;QAEF,KAAK,CAAC,iBAAiB;YACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;gBAC7B,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC3B,iDAAiD;gBACjD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QAED,oBAAoB;YAClB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACxB,CAAC;QAED,cAAc,CAAC,KAAK;YAClB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,MAAM;YACJ,IAAI,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,QAAQ,CAAC,EAAE,CAAC;gBACjE,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,0CAA0C,QAAQ,GAAG,CAAC,CAAC;YACzF,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,CAAC,AAAD,EAAG,CAAC;YAClB,CAAC;YAED,OAAO,CACL,CAAC,gBAAgB,CACf,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE;oBACZ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;gBACpB,CAAC,CAAC,CACF,IAAI,IAAI,CAAC,KAAK,CAAC,EACf,CACH,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import * as Font from 'expo-font';\nimport React, { ComponentClass } from 'react';\nimport {\n  Text,\n  TextProps,\n  TouchableHighlightProps,\n  ViewProps,\n  OpaqueColorValue,\n  TextStyle,\n  ViewStyle,\n} from 'react-native';\n\nimport createIconSet from './vendor/react-native-vector-icons/lib/create-icon-set';\nimport createIconButtonComponent from './vendor/react-native-vector-icons/lib/icon-button';\n\nexport {\n  DEFAULT_ICON_COLOR,\n  DEFAULT_ICON_SIZE,\n} from './vendor/react-native-vector-icons/lib/create-icon-set';\n\nexport interface IconProps<GLYPHS extends string> extends TextProps {\n  /**\n   * Size of the icon, can also be passed as fontSize in the style object.\n   *\n   * @default 12\n   */\n  size?: number;\n\n  /**\n   * Name of the icon to show\n   *\n   * See Icon Explorer app\n   * {@link https://expo.github.io/vector-icons/}\n   */\n  name: GLYPHS;\n\n  /**\n   * Color of the icon. Can be a string or OpaqueColorValue (returned from\n   * PlatformColor(..))\n   *\n   */\n  color?: string | OpaqueColorValue;\n}\n\nexport interface IconButtonProps<GLYPHS extends string>\n  extends IconProps<GLYPHS>,\n    ViewProps,\n    TouchableHighlightProps {\n  /**\n   * Text and icon color\n   * Use iconStyle or nest a Text component if you need different colors.\n   * Can be a string or OpaqueColorValue (returned from PlatformColor(..))\n   *\n   *  @default 'white'\n   */\n  color?: string | OpaqueColorValue;\n\n  /**\n   * Border radius of the button\n   * Set to 0 to disable.\n   *\n   * @default 5\n   */\n  borderRadius?: number;\n\n  /**\n   * Styles applied to the icon only\n   * Good for setting margins or a different color.\n   *\n   * @default {marginRight: 10}\n   */\n  iconStyle?: TextStyle;\n\n  /**\n   * Style prop inherited from TextProps and TouchableWithoutFeedbackProperties\n   * Only exist here so we can have ViewStyle or TextStyle\n   *\n   */\n  style?: ViewStyle | TextStyle;\n\n  /**\n   * Background color of the button. Can be a string or OpaqueColorValue (returned from\n   * PlatformColor(..))\n   *\n   * @default '#007AFF'\n   */\n  backgroundColor?: string | OpaqueColorValue;\n}\n\nexport type GlyphMap<G extends string> = { [K in G]: number | string };\n\nexport interface Icon<G extends string, FN extends string> {\n  defaultProps: any;\n  Button: ComponentClass<IconButtonProps<G>>;\n  glyphMap: GlyphMap<G>;\n  getRawGlyphMap: () => GlyphMap<G>;\n  getFontFamily: () => FN;\n  loadFont: () => Promise<void>;\n  font: { [x: string]: any };\n  new (props: IconProps<G>): React.Component<IconProps<G>>;\n}\n\nexport default function <G extends string, FN extends string>(\n  glyphMap: GlyphMap<G>,\n  fontName: FN,\n  expoAssetId,\n  fontStyle?: any\n): Icon<G, FN> {\n  const font = { [fontName]: expoAssetId };\n  const RNVIconComponent = createIconSet(glyphMap, fontName, null, fontStyle);\n\n  return class Icon extends React.Component<IconProps<G>> {\n    static defaultProps = RNVIconComponent.defaultProps;\n    static Button = createIconButtonComponent(Icon);\n    static glyphMap = glyphMap;\n    static getRawGlyphMap = () => glyphMap;\n    static getFontFamily = () => fontName;\n    static loadFont = () => Font.loadAsync(font);\n    static font = font;\n\n    _mounted = false;\n    _icon?: any;\n\n    state = {\n      fontIsLoaded: Font.isLoaded(fontName),\n    };\n\n    async componentDidMount() {\n      this._mounted = true;\n      if (!this.state.fontIsLoaded) {\n        await Font.loadAsync(font);\n        /* eslint-disable react/no-did-mount-set-state */\n        this._mounted && this.setState({ fontIsLoaded: true });\n      }\n    }\n\n    componentWillUnmount() {\n      this._mounted = false;\n    }\n\n    setNativeProps(props) {\n      if (this._icon) {\n        this._icon.setNativeProps(props);\n      }\n    }\n\n    render() {\n      if (__DEV__ && this.props.name && !(this.props.name in glyphMap)) {\n        console.warn(`\"${this.props.name}\" is not a valid icon name for family \"${fontName}\"`);\n      }\n\n      if (!this.state.fontIsLoaded) {\n        return <Text />;\n      }\n\n      return (\n        <RNVIconComponent\n          ref={(view) => {\n            this._icon = view;\n          }}\n          {...this.props}\n        />\n      );\n    }\n  };\n}\n"]}