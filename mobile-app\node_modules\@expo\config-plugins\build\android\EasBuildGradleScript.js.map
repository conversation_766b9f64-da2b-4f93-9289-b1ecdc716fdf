{"version": 3, "file": "EasBuildGradleScript.js", "names": [], "sources": ["../../src/android/EasBuildGradleScript.ts"], "sourcesContent": ["export default `// Build integration with EAS\n\nimport java.nio.file.Paths\n\nandroid {\n  signingConfigs {\n    release {\n      // This is necessary to avoid needing the user to define a release signing config manually\n      // If no release config is defined, and this is not present, build for assembleRelease will crash\n    }\n  }\n\n  buildTypes {\n    release {\n      // This is necessary to avoid needing the user to define a release build type manually\n    }\n  }\n}\n\ndef isEasBuildConfigured = false\n\ntasks.whenTaskAdded {\n  def debug = gradle.startParameter.taskNames.any { it.toLowerCase().contains('debug') }\n\n  if (debug) {\n    return\n  }\n\n  // We only need to configure EAS build once\n  if (isEasBuildConfigured) {\n    return\n  }\n\n  isEasBuildConfigured = true;\n\n  android.signingConfigs.release {\n    def credentialsJson = rootProject.file(\"../credentials.json\");\n\n    if (credentialsJson.exists()) {\n      if (storeFile && !System.getenv(\"EAS_BUILD\")) {\n        println(\"Path to release keystore file is already set, ignoring 'credentials.json'\")\n      } else {\n        try {\n          def credentials = new groovy.json.JsonSlurper().parse(credentialsJson)\n          def keystorePath = Paths.get(credentials.android.keystore.keystorePath);\n          def storeFilePath = keystorePath.isAbsolute()\n            ? keystorePath\n            : rootProject.file(\"..\").toPath().resolve(keystorePath);\n\n          storeFile storeFilePath.toFile()\n          storePassword credentials.android.keystore.keystorePassword\n          keyAlias credentials.android.keystore.keyAlias\n          if (credentials.android.keystore.containsKey(\"keyPassword\")) {\n            keyPassword credentials.android.keystore.keyPassword\n          } else {\n            // key password is required by Gradle, but PKCS keystores don't have one\n            // using the keystore password seems to satisfy the requirement\n            keyPassword credentials.android.keystore.keystorePassword\n          }\n        } catch (Exception e) {\n          println(\"An error occurred while parsing 'credentials.json': \" + e.message)\n        }\n      }\n    } else {\n      if (storeFile == null) {\n        println(\"Couldn't find a 'credentials.json' file, skipping release keystore configuration\")\n      }\n    }\n  }\n\n  android.buildTypes.release {\n    signingConfig android.signingConfigs.release\n  }\n}\n`;\n"], "mappings": ";;;;;;iCAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC", "ignoreList": []}