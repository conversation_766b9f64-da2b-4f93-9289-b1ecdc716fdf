{"version": 3, "file": "Locales.js", "names": ["_path", "data", "_interopRequireDefault", "require", "_", "_XML", "_locales", "e", "__esModule", "default", "withLocales", "config", "withDangerousMod", "modResults", "setLocalesAsync", "projectRoot", "modRequest", "exports", "getLocales", "locales", "localesMap", "getResolvedLocalesAsync", "lang", "localizationObj", "Object", "entries", "stringsFilePath", "path", "join", "AndroidConfig", "Paths", "getResourceFolderAsync", "replaceAll", "writeXMLAsync", "xml", "resources", "map", "k", "v", "string", "$", "name"], "sources": ["../../src/android/Locales.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport path from 'path';\n\nimport { AndroidConfig, withDangerousMod } from '..';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { writeXMLAsync } from '../utils/XML';\nimport { getResolvedLocalesAsync, LocaleJson } from '../utils/locales';\n\nexport const withLocales: ConfigPlugin = (config) => {\n  return withDangerousMod(config, [\n    'android',\n    async (config) => {\n      config.modResults = await setLocalesAsync(config, {\n        projectRoot: config.modRequest.projectRoot,\n      });\n      return config;\n    },\n  ]);\n};\n\nexport function getLocales(\n  config: Pick<ExpoConfig, 'locales'>\n): Record<string, string | LocaleJson> | null {\n  return config.locales ?? null;\n}\n\nexport async function setLocalesAsync(\n  config: Pick<ExpoConfig, 'locales'>,\n  { projectRoot }: { projectRoot: string }\n): Promise<unknown> {\n  const locales = getLocales(config);\n  if (!locales) {\n    return config;\n  }\n  const localesMap = await getResolvedLocalesAsync(projectRoot, locales, 'android');\n  for (const [lang, localizationObj] of Object.entries(localesMap)) {\n    const stringsFilePath = path.join(\n      await AndroidConfig.Paths.getResourceFolderAsync(projectRoot),\n      `values-b+${lang.replaceAll('-', '+')}`,\n      'strings.xml'\n    );\n    writeXMLAsync({\n      path: stringsFilePath,\n      xml: {\n        resources: Object.entries(localizationObj).map(([k, v]) => ({\n          string: {\n            $: {\n              name: k,\n            },\n            _: `\"${v}\"`,\n          },\n        })),\n      },\n    });\n  }\n\n  return config;\n}\n"], "mappings": ";;;;;;;;AACA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,EAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,CAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,KAAA;EAAA,MAAAJ,IAAA,GAAAE,OAAA;EAAAE,IAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,SAAA;EAAA,MAAAL,IAAA,GAAAE,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuE,SAAAC,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEhE,MAAMG,WAAyB,GAAIC,MAAM,IAAK;EACnD,OAAO,IAAAC,oBAAgB,EAACD,MAAM,EAAE,CAC9B,SAAS,EACT,MAAOA,MAAM,IAAK;IAChBA,MAAM,CAACE,UAAU,GAAG,MAAMC,eAAe,CAACH,MAAM,EAAE;MAChDI,WAAW,EAAEJ,MAAM,CAACK,UAAU,CAACD;IACjC,CAAC,CAAC;IACF,OAAOJ,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;AAACM,OAAA,CAAAP,WAAA,GAAAA,WAAA;AAEK,SAASQ,UAAUA,CACxBP,MAAmC,EACS;EAC5C,OAAOA,MAAM,CAACQ,OAAO,IAAI,IAAI;AAC/B;AAEO,eAAeL,eAAeA,CACnCH,MAAmC,EACnC;EAAEI;AAAqC,CAAC,EACtB;EAClB,MAAMI,OAAO,GAAGD,UAAU,CAACP,MAAM,CAAC;EAClC,IAAI,CAACQ,OAAO,EAAE;IACZ,OAAOR,MAAM;EACf;EACA,MAAMS,UAAU,GAAG,MAAM,IAAAC,kCAAuB,EAACN,WAAW,EAAEI,OAAO,EAAE,SAAS,CAAC;EACjF,KAAK,MAAM,CAACG,IAAI,EAAEC,eAAe,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,UAAU,CAAC,EAAE;IAChE,MAAMM,eAAe,GAAGC,eAAI,CAACC,IAAI,CAC/B,MAAMC,iBAAa,CAACC,KAAK,CAACC,sBAAsB,CAAChB,WAAW,CAAC,EAC7D,YAAYO,IAAI,CAACU,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,EACvC,aACF,CAAC;IACD,IAAAC,oBAAa,EAAC;MACZN,IAAI,EAAED,eAAe;MACrBQ,GAAG,EAAE;QACHC,SAAS,EAAEX,MAAM,CAACC,OAAO,CAACF,eAAe,CAAC,CAACa,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,MAAM;UAC1DC,MAAM,EAAE;YACNC,CAAC,EAAE;cACDC,IAAI,EAAEJ;YACR,CAAC;YACDjC,CAAC,EAAE,IAAIkC,CAAC;UACV;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEA,OAAO3B,MAAM;AACf", "ignoreList": []}