/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

'use strict';

import AnimatedValue from '../nodes/AnimatedValue';
import AnimatedValueXY from '../nodes/AnimatedValueXY';
import AnimatedInterpolation from '../nodes/AnimatedInterpolation';
import Easing from '../../../../exports/Easing';
import Animation from './Animation';
import { shouldUseNativeDriver } from '../NativeAnimatedHelper';
import type { PlatformConfig } from '../AnimatedPlatformConfig';
import type { AnimationConfig, EndCallback } from './Animation';
import type { RgbaValue } from '../nodes/AnimatedColor';
import AnimatedColor from '../nodes/AnimatedColor';
export type TimingAnimationConfig = $ReadOnly<{
  ...AnimationConfig,
  toValue: number | AnimatedValue | {
    x: number,
    y: number,
    ...
  } | AnimatedValueXY | RgbaValue | AnimatedColor | AnimatedInterpolation<number>,
  easing?: (value: number) => number,
  duration?: number,
  delay?: number,
}>;
export type TimingAnimationConfigSingle = $ReadOnly<{
  ...AnimationConfig,
  toValue: number,
  easing?: (value: number) => number,
  duration?: number,
  delay?: number,
}>;
let _easeInOut;
declare function easeInOut(): any;
declare class TimingAnimation extends Animation {
  _startTime: number,
  _fromValue: number,
  _toValue: number,
  _duration: number,
  _delay: number,
  _easing: (value: number) => number,
  _onUpdate: (value: number) => void,
  _animationFrame: any,
  _timeout: any,
  _useNativeDriver: boolean,
  _platformConfig: ?PlatformConfig,
  constructor(config: TimingAnimationConfigSingle): any,
  __getNativeAnimationConfig(): any,
  start(fromValue: number, onUpdate: (value: number) => void, onEnd: ?EndCallback, previousAnimation: ?Animation, animatedValue: AnimatedValue): void,
  onUpdate(): void,
  stop(): void,
}
export default TimingAnimation;