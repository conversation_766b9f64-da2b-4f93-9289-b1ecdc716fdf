{"version": 3, "file": "withIosSplashScreenStoryboardImage.js", "names": ["_InterfaceBuilder", "data", "require", "_withIosSplashScreenStoryboard", "withIosSplashScreenImage", "config", "props", "withIosSplashScreenStoryboard", "modResults", "applySplashScreenStoryboard", "exports", "obj", "splash", "resizeMode", "splashScreenImagePresent", "Boolean", "image", "imageName", "contentMode", "getImageContentMode", "applyImageToSplashScreenXML", "backgroundColor", "enableFullScreenImage", "enableFullScreenImage_legacy", "imageWidth", "removeImageFromSplashScreen", "Error"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashScreenStoryboardImage.ts"], "sourcesContent": ["import { ConfigPlugin } from '@expo/config-plugins';\n\nimport {\n  applyImageToSplashScreenXML,\n  IBSplashScreenDocument,\n  ImageContentMode,\n  removeImageFromSplashScreen,\n} from './InterfaceBuilder';\nimport { IOSSplashConfig } from './getIosSplashConfig';\nimport { withIosSplashScreenStoryboard } from './withIosSplashScreenStoryboard';\n\nexport const withIosSplashScreenImage: ConfigPlugin<IOSSplashConfig> = (config, props) => {\n  return withIosSplashScreenStoryboard(config, (config) => {\n    config.modResults = applySplashScreenStoryboard(config.modResults, props);\n    return config;\n  });\n};\n\nexport function applySplashScreenStoryboard(obj: IBSplashScreenDocument, splash: IOSSplashConfig) {\n  const resizeMode = splash?.resizeMode;\n  const splashScreenImagePresent = Boolean(splash?.image);\n  const imageName = 'SplashScreenLogo';\n  // Only get the resize mode when the image is present.\n  if (splashScreenImagePresent) {\n    const contentMode = getImageContentMode(resizeMode || 'contain');\n    return applyImageToSplashScreenXML(obj, {\n      contentMode,\n      imageName,\n      backgroundColor: splash.backgroundColor,\n      enableFullScreenImage: splash.enableFullScreenImage_legacy ?? false,\n      imageWidth: splash.imageWidth,\n    });\n  }\n\n  return removeImageFromSplashScreen(obj, { imageName });\n}\n\nfunction getImageContentMode(resizeMode: string): ImageContentMode {\n  switch (resizeMode) {\n    case 'contain':\n      return 'scaleAspectFit';\n    case 'cover':\n      return 'scaleAspectFill';\n    default:\n      throw new Error(`{ resizeMode: \"${resizeMode}\" } is not supported for iOS platform.`);\n  }\n}\n"], "mappings": ";;;;;;;AAEA,SAAAA,kBAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,iBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAOA,SAAAE,+BAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,8BAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAMG,wBAAuD,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACxF,OAAO,IAAAC,8DAA6B,EAACF,MAAM,EAAGA,MAAM,IAAK;IACvDA,MAAM,CAACG,UAAU,GAAGC,2BAA2B,CAACJ,MAAM,CAACG,UAAU,EAAEF,KAAK,CAAC;IACzE,OAAOD,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAN,wBAAA,GAAAA,wBAAA;AAEK,SAASK,2BAA2BA,CAACE,GAA2B,EAAEC,MAAuB,EAAE;EAChG,MAAMC,UAAU,GAAGD,MAAM,EAAEC,UAAU;EACrC,MAAMC,wBAAwB,GAAGC,OAAO,CAACH,MAAM,EAAEI,KAAK,CAAC;EACvD,MAAMC,SAAS,GAAG,kBAAkB;EACpC;EACA,IAAIH,wBAAwB,EAAE;IAC5B,MAAMI,WAAW,GAAGC,mBAAmB,CAACN,UAAU,IAAI,SAAS,CAAC;IAChE,OAAO,IAAAO,+CAA2B,EAACT,GAAG,EAAE;MACtCO,WAAW;MACXD,SAAS;MACTI,eAAe,EAAET,MAAM,CAACS,eAAe;MACvCC,qBAAqB,EAAEV,MAAM,CAACW,4BAA4B,IAAI,KAAK;MACnEC,UAAU,EAAEZ,MAAM,CAACY;IACrB,CAAC,CAAC;EACJ;EAEA,OAAO,IAAAC,+CAA2B,EAACd,GAAG,EAAE;IAAEM;EAAU,CAAC,CAAC;AACxD;AAEA,SAASE,mBAAmBA,CAACN,UAAkB,EAAoB;EACjE,QAAQA,UAAU;IAChB,KAAK,SAAS;MACZ,OAAO,gBAAgB;IACzB,KAAK,OAAO;MACV,OAAO,iBAAiB;IAC1B;MACE,MAAM,IAAIa,KAAK,CAAC,kBAAkBb,UAAU,wCAAwC,CAAC;EACzF;AACF", "ignoreList": []}